package qidian.it.springboot_mybatis;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import qidian.it.springboot_mybatis.dao.BookDao;
import qidian.it.springboot_mybatis.domain.Book;

@SpringBootTest
class SpringbootMybatisApplicationTests {
    @Autowired
    private BookDao bookDao;


    @Test
    void testGetById() {
        Book book = bookDao.getById(1);
        System.out.println(book);
    }

}
