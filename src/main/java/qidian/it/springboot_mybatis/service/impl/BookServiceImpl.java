package qidian.it.springboot_mybatis.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import qidian.it.springboot_mybatis.dao.BookDao;
import qidian.it.springboot_mybatis.domain.Book;
import qidian.it.springboot_mybatis.service.BookService;

public class BookServiceImpl implements BookService {
    @Autowired
    private BookDao bookDao;

    public boolean save(Book book) {
        return bookDao.save(book);
    }

    @Override
    public void getById() {
        System.out.println("book service");
    }

}
