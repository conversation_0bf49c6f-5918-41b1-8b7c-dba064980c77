package qidian.it.springboot_01.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qidian.it.springboot_01.domain.Enterprice;

@RestController//作用在类上，表示当前类是一个控制器
@RequestMapping("/book")
public class BookController {

    @Value("${enterprise.name}")
    private String name;

    @Autowired
    private Environment env ;

    @Autowired
    private Enterprice enterprice;
    @GetMapping("/{id}")
    public String getById(@PathVariable Integer id) //@PathVariable注解表示从路径中获取参数
    {
        System.out.println("id="+id);
        System.out.println("name="+name);
        System.out.println("name="+env.getProperty("enterprise.name"));
        System.out.println("age="+env.getProperty("enterprise.age"));
        System.out.println("subject="+env.getProperty("enterprise.subject"));
        System.out.println("--------------");
        System.out.println(enterprice);
        return "book ok";
    }
}
