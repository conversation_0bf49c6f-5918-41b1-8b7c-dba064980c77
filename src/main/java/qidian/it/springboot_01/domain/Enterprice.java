package qidian.it.springboot_01.domain;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component//作用在类上，表示当前类是一个组件
@ConfigurationProperties(prefix = "enterprise")//作用在类上，表示当前类中的属性和配置文件中的属性进行绑定
public class Enterprice {
    private String name;
    private Integer age;
    private String[] subject;

    @Override
    public String toString() {
        return "Enterprice{" +
                "name='" + name + '\'' +
                ", age=" + age +
                ", subject=" + Arrays.toString(subject) +
                '}';
    }

    public Enterprice() {
    }

    public Enterprice(String name, Integer age, String[] subject) {
        this.name = name;
        this.age = age;
        this.subject = subject;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String[] getSubject() {
        return subject;
    }

    public void setSubject(String[] subject) {
        this.subject = subject;
    }
}
